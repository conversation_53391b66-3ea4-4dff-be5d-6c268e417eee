import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsObject, 
  ValidateNested, 
  IsNumber, 
  Min, 
  Max, 
  IsArray, 
  IsUUID 
} from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';

/**
 * DTO cho structured content của user memory
 */
export class UserMemoryStructuredContentDto {
  @ApiPropertyOptional({
    description: 'Tiêu đề của memory',
    example: 'Sở thích âm nhạc',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Lý do ghi nhớ thông tin này',
    example: 'Để cá nhân hóa trải nghiệm âm nhạc',
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Nội dung chính của memory',
    example: 'Người dùng thích nghe nhạc pop và rock',
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: 'Loại memory',
    example: 'preference',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Mức độ quan trọng (1-10)',
    example: 8,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  importance?: number;

  @ApiPropertyOptional({
    description: 'Các từ khóa liên quan',
    example: ['âm nhạc', 'pop', 'rock'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @ApiPropertyOptional({
    description: 'Ngữ cảnh sử dụng',
    example: 'Khi đề xuất nhạc hoặc thảo luận về âm nhạc',
  })
  @IsOptional()
  @IsString()
  context?: string;
}

/**
 * DTO cho metadata của user memory
 */
export class UserMemoryMetadataDto {
  @ApiPropertyOptional({
    description: 'ID của agent tạo ra memory này',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  agentId?: string;

  @ApiPropertyOptional({
    description: 'Nguồn gốc của thông tin',
    example: 'conversation',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1703123456789,
  })
  @IsOptional()
  @IsNumber()
  lastUpdated?: number;

  @ApiPropertyOptional({
    description: 'Số lần sử dụng memory',
    example: 5,
  })
  @IsOptional()
  @IsNumber()
  usageCount?: number;

  @ApiPropertyOptional({
    description: 'Tags để phân loại',
    example: ['personal', 'preference'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO để tạo user memory mới
 */
export class CreateUserMemoryDto {
  @ApiProperty({
    description: 'Nội dung có cấu trúc của memory',
    type: UserMemoryStructuredContentDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => UserMemoryStructuredContentDto)
  structuredContent: UserMemoryStructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: UserMemoryMetadataDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => UserMemoryMetadataDto)
  metadata?: UserMemoryMetadataDto;
}

/**
 * DTO để cập nhật user memory
 */
export class UpdateUserMemoryDto {
  @ApiPropertyOptional({
    description: 'Nội dung có cấu trúc của memory',
    type: UserMemoryStructuredContentDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => UserMemoryStructuredContentDto)
  structuredContent?: UserMemoryStructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: UserMemoryMetadataDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => UserMemoryMetadataDto)
  metadata?: UserMemoryMetadataDto;
}

/**
 * DTO cho query danh sách user memories
 */
export class QueryUserMemoryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo loại memory',
    example: 'preference',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo agent ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  agentId?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo tags',
    example: ['personal', 'preference'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO response cho user memory
 */
export class UserMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 12345,
  })
  userId: number;

  @ApiProperty({
    description: 'Nội dung có cấu trúc của memory',
    type: UserMemoryStructuredContentDto,
  })
  structuredContent: UserMemoryStructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: UserMemoryMetadataDto,
  })
  metadata?: UserMemoryMetadataDto;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;
}
