import { QueryDto } from '@common/dto';
import { StructuredContentInterface } from '@modules/agent/interfaces/agent-memory.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsObject,
  IsOptional,
  IsString,
  ValidateNested
} from 'class-validator';

/**
 * DTO cho structured content của user memory
 */
export class UserMemoryStructuredContentDto implements StructuredContentInterface {
  @ApiPropertyOptional({
    description: 'Tiêu đề của memory',
    example: 'Sở thích âm nhạc',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Lý do ghi nhớ thông tin này',
    example: 'Để cá nhân hóa trải nghiệm âm nhạc',
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Nội dung chính của memory',
    example: 'Người dùng thích nghe nhạc pop và rock',
  })
  @IsString()
  content: string;
}

/**
 * DTO để tạo user memory mới
 */
export class CreateUserMemoryDto {
  @ApiProperty({
    description: 'Nội dung có cấu trúc của memory',
    type: UserMemoryStructuredContentDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => UserMemoryStructuredContentDto)
  structuredContent: UserMemoryStructuredContentDto;
}

/**
 * DTO để cập nhật user memory
 */
export class UpdateUserMemoryDto {
  @ApiProperty({
    description: 'Nội dung có cấu trúc của memory',
    type: UserMemoryStructuredContentDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => UserMemoryStructuredContentDto)
  structuredContent: UserMemoryStructuredContentDto;
}

/**
 * DTO cho query danh sách user memories
 */
export class QueryUserMemoryDto extends QueryDto {
}

/**
 * DTO response cho user memory
 */
export class UserMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 12345,
  })
  userId: number;

  @ApiProperty({
    description: 'Nội dung có cấu trúc của memory',
    type: UserMemoryStructuredContentDto,
  })
  structuredContent: UserMemoryStructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: Object,
  })
  metadata?: Record<string, any>;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;
}
