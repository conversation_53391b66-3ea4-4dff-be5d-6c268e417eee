/**
 * Error codes cho memories operations
 * <PERSON><PERSON>i mã lỗi: 7000-7099
 */
export enum MemoriesErrorCode {
  // User Memories Errors (7000-7049)
  USER_MEMORY_NOT_FOUND = 'USER_MEMORY_NOT_FOUND_7000',
  USER_MEMORY_ACCESS_DENIED = 'USER_MEMORY_ACCESS_DENIED_7001',
  USER_MEMORY_INVALID_DATA = 'USER_MEMORY_INVALID_DATA_7002',
  USER_MEMORY_CREATE_FAILED = 'USER_MEMORY_CREATE_FAILED_7003',
  USER_MEMORY_UPDATE_FAILED = 'USER_MEMORY_UPDATE_FAILED_7004',
  USER_MEMORY_DELETE_FAILED = 'USER_MEMORY_DELETE_FAILED_7005',
  USER_MEMORY_SEARCH_FAILED = 'USER_MEMORY_SEARCH_FAILED_7006',
  USER_MEMORY_INVALID_CONTENT = 'USER_MEMORY_INVALID_CONTENT_7007',
  USER_MEMORY_INVALID_METADATA = 'USER_MEMORY_INVALID_METADATA_7008',
  USER_MEMORY_DUPLICATE = 'USER_MEMORY_DUPLICATE_7009',

  // Agent Memories Errors (7050-7099)
  AGENT_MEMORY_NOT_FOUND = 'AGENT_MEMORY_NOT_FOUND_7050',
  AGENT_MEMORY_ACCESS_DENIED = 'AGENT_MEMORY_ACCESS_DENIED_7051',
  AGENT_MEMORY_INVALID_DATA = 'AGENT_MEMORY_INVALID_DATA_7052',
  AGENT_MEMORY_CREATE_FAILED = 'AGENT_MEMORY_CREATE_FAILED_7053',
  AGENT_MEMORY_UPDATE_FAILED = 'AGENT_MEMORY_UPDATE_FAILED_7054',
  AGENT_MEMORY_DELETE_FAILED = 'AGENT_MEMORY_DELETE_FAILED_7055',
  AGENT_MEMORY_SEARCH_FAILED = 'AGENT_MEMORY_SEARCH_FAILED_7056',
  AGENT_MEMORY_INVALID_CONTENT = 'AGENT_MEMORY_INVALID_CONTENT_7057',
  AGENT_MEMORY_INVALID_METADATA = 'AGENT_MEMORY_INVALID_METADATA_7058',
  AGENT_MEMORY_DUPLICATE = 'AGENT_MEMORY_DUPLICATE_7059',
  AGENT_NOT_OWNED_BY_USER = 'AGENT_NOT_OWNED_BY_USER_7060',

  // General Memory Errors (7090-7099)
  MEMORY_OPERATION_FAILED = 'MEMORY_OPERATION_FAILED_7090',
  MEMORY_VALIDATION_FAILED = 'MEMORY_VALIDATION_FAILED_7091',
  MEMORY_DATABASE_ERROR = 'MEMORY_DATABASE_ERROR_7092',
  MEMORY_PERMISSION_DENIED = 'MEMORY_PERMISSION_DENIED_7093',
  MEMORY_LIMIT_EXCEEDED = 'MEMORY_LIMIT_EXCEEDED_7094',
  MEMORY_INVALID_FORMAT = 'MEMORY_INVALID_FORMAT_7095',
}

/**
 * Messages tương ứng với error codes
 */
export const MemoriesErrorMessages = {
  // User Memories Messages
  [MemoriesErrorCode.USER_MEMORY_NOT_FOUND]: 'Memory của user không tồn tại',
  [MemoriesErrorCode.USER_MEMORY_ACCESS_DENIED]: 'Không có quyền truy cập memory này',
  [MemoriesErrorCode.USER_MEMORY_INVALID_DATA]: 'Dữ liệu memory không hợp lệ',
  [MemoriesErrorCode.USER_MEMORY_CREATE_FAILED]: 'Không thể tạo memory mới',
  [MemoriesErrorCode.USER_MEMORY_UPDATE_FAILED]: 'Không thể cập nhật memory',
  [MemoriesErrorCode.USER_MEMORY_DELETE_FAILED]: 'Không thể xóa memory',
  [MemoriesErrorCode.USER_MEMORY_SEARCH_FAILED]: 'Không thể tìm kiếm memory',
  [MemoriesErrorCode.USER_MEMORY_INVALID_CONTENT]: 'Nội dung memory không hợp lệ',
  [MemoriesErrorCode.USER_MEMORY_INVALID_METADATA]: 'Metadata memory không hợp lệ',
  [MemoriesErrorCode.USER_MEMORY_DUPLICATE]: 'Memory đã tồn tại',

  // Agent Memories Messages
  [MemoriesErrorCode.AGENT_MEMORY_NOT_FOUND]: 'Memory của agent không tồn tại',
  [MemoriesErrorCode.AGENT_MEMORY_ACCESS_DENIED]: 'Không có quyền truy cập memory của agent này',
  [MemoriesErrorCode.AGENT_MEMORY_INVALID_DATA]: 'Dữ liệu memory agent không hợp lệ',
  [MemoriesErrorCode.AGENT_MEMORY_CREATE_FAILED]: 'Không thể tạo memory cho agent',
  [MemoriesErrorCode.AGENT_MEMORY_UPDATE_FAILED]: 'Không thể cập nhật memory agent',
  [MemoriesErrorCode.AGENT_MEMORY_DELETE_FAILED]: 'Không thể xóa memory agent',
  [MemoriesErrorCode.AGENT_MEMORY_SEARCH_FAILED]: 'Không thể tìm kiếm memory agent',
  [MemoriesErrorCode.AGENT_MEMORY_INVALID_CONTENT]: 'Nội dung memory agent không hợp lệ',
  [MemoriesErrorCode.AGENT_MEMORY_INVALID_METADATA]: 'Metadata memory agent không hợp lệ',
  [MemoriesErrorCode.AGENT_MEMORY_DUPLICATE]: 'Memory agent đã tồn tại',
  [MemoriesErrorCode.AGENT_NOT_OWNED_BY_USER]: 'Agent không thuộc về user này',

  // General Memory Messages
  [MemoriesErrorCode.MEMORY_OPERATION_FAILED]: 'Thao tác memory thất bại',
  [MemoriesErrorCode.MEMORY_VALIDATION_FAILED]: 'Validation memory thất bại',
  [MemoriesErrorCode.MEMORY_DATABASE_ERROR]: 'Lỗi database khi xử lý memory',
  [MemoriesErrorCode.MEMORY_PERMISSION_DENIED]: 'Không có quyền thực hiện thao tác này',
  [MemoriesErrorCode.MEMORY_LIMIT_EXCEEDED]: 'Đã vượt quá giới hạn số lượng memory',
  [MemoriesErrorCode.MEMORY_INVALID_FORMAT]: 'Định dạng memory không hợp lệ',
};

/**
 * Helper function để tạo error message với context
 */
export class MemoriesErrorHelper {
  /**
   * Tạo error message cho user memory
   */
  static createUserMemoryError(code: MemoriesErrorCode, context?: string): string {
    const baseMessage = MemoriesErrorMessages[code];
    return context ? `${baseMessage}: ${context}` : baseMessage;
  }

  /**
   * Tạo error message cho agent memory
   */
  static createAgentMemoryError(code: MemoriesErrorCode, context?: string): string {
    const baseMessage = MemoriesErrorMessages[code];
    return context ? `${baseMessage}: ${context}` : baseMessage;
  }

  /**
   * Kiểm tra xem error code có phải là user memory error không
   */
  static isUserMemoryError(code: MemoriesErrorCode): boolean {
    const userMemoryErrorCodes = [
      MemoriesErrorCode.USER_MEMORY_NOT_FOUND,
      MemoriesErrorCode.USER_MEMORY_ACCESS_DENIED,
      MemoriesErrorCode.USER_MEMORY_INVALID_DATA,
      MemoriesErrorCode.USER_MEMORY_CREATE_FAILED,
      MemoriesErrorCode.USER_MEMORY_UPDATE_FAILED,
      MemoriesErrorCode.USER_MEMORY_DELETE_FAILED,
      MemoriesErrorCode.USER_MEMORY_SEARCH_FAILED,
      MemoriesErrorCode.USER_MEMORY_INVALID_CONTENT,
      MemoriesErrorCode.USER_MEMORY_INVALID_METADATA,
      MemoriesErrorCode.USER_MEMORY_DUPLICATE,
    ];
    return userMemoryErrorCodes.includes(code);
  }

  /**
   * Kiểm tra xem error code có phải là agent memory error không
   */
  static isAgentMemoryError(code: MemoriesErrorCode): boolean {
    const agentMemoryErrorCodes = [
      MemoriesErrorCode.AGENT_MEMORY_NOT_FOUND,
      MemoriesErrorCode.AGENT_MEMORY_ACCESS_DENIED,
      MemoriesErrorCode.AGENT_MEMORY_INVALID_DATA,
      MemoriesErrorCode.AGENT_MEMORY_CREATE_FAILED,
      MemoriesErrorCode.AGENT_MEMORY_UPDATE_FAILED,
      MemoriesErrorCode.AGENT_MEMORY_DELETE_FAILED,
      MemoriesErrorCode.AGENT_MEMORY_SEARCH_FAILED,
      MemoriesErrorCode.AGENT_MEMORY_INVALID_CONTENT,
      MemoriesErrorCode.AGENT_MEMORY_INVALID_METADATA,
      MemoriesErrorCode.AGENT_MEMORY_DUPLICATE,
      MemoriesErrorCode.AGENT_NOT_OWNED_BY_USER,
    ];
    return agentMemoryErrorCodes.includes(code);
  }
}
