import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * <PERSON>rror codes cho memories operations
 * <PERSON><PERSON>i mã lỗi: 7000-7099
 */
export const MEMORIES_ERROR_CODES = {
  // User Memories Errors (7000-7049)
  USER_MEMORY_NOT_FOUND: new ErrorCode(
    7000,
    'Memory của user không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  USER_MEMORY_ACCESS_DENIED: new ErrorCode(
    7001,
    'Không có quyền truy cập memory này',
    HttpStatus.FORBIDDEN,
  ),

  USER_MEMORY_INVALID_DATA: new ErrorCode(
    7002,
    'Dữ liệu memory không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  USER_MEMORY_CREATE_FAILED: new ErrorCode(
    7003,
    'Không thể tạo memory mới',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  USER_MEMORY_UPDATE_FAILED: new ErrorCode(
    7004,
    'Không thể cập nhật memory',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  USER_MEMORY_DELETE_FAILED: new ErrorCode(
    7005,
    'Không thể xóa memory',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  USER_MEMORY_SEARCH_FAILED: new ErrorCode(
    7006,
    'Không thể tìm kiếm memory',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  USER_MEMORY_INVALID_CONTENT: new ErrorCode(
    7007,
    'Nội dung memory không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  USER_MEMORY_INVALID_METADATA: new ErrorCode(
    7008,
    'Metadata memory không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  USER_MEMORY_DUPLICATE: new ErrorCode(
    7009,
    'Memory đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  // Agent Memories Errors (7050-7099)
  AGENT_MEMORY_NOT_FOUND: new ErrorCode(
    7050,
    'Memory của agent không tồn tại',
    HttpStatus.NOT_FOUND,
  ),

  AGENT_MEMORY_ACCESS_DENIED: new ErrorCode(
    7051,
    'Không có quyền truy cập memory của agent này',
    HttpStatus.FORBIDDEN,
  ),

  AGENT_MEMORY_INVALID_DATA: new ErrorCode(
    7052,
    'Dữ liệu memory agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  AGENT_MEMORY_CREATE_FAILED: new ErrorCode(
    7053,
    'Không thể tạo memory cho agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  AGENT_MEMORY_UPDATE_FAILED: new ErrorCode(
    7054,
    'Không thể cập nhật memory agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  AGENT_MEMORY_DELETE_FAILED: new ErrorCode(
    7055,
    'Không thể xóa memory agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  AGENT_MEMORY_SEARCH_FAILED: new ErrorCode(
    7056,
    'Không thể tìm kiếm memory agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  AGENT_MEMORY_INVALID_CONTENT: new ErrorCode(
    7057,
    'Nội dung memory agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  AGENT_MEMORY_INVALID_METADATA: new ErrorCode(
    7058,
    'Metadata memory agent không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  AGENT_MEMORY_DUPLICATE: new ErrorCode(
    7059,
    'Memory agent đã tồn tại',
    HttpStatus.CONFLICT,
  ),

  AGENT_NOT_OWNED_BY_USER: new ErrorCode(
    7060,
    'Agent không thuộc về user này',
    HttpStatus.FORBIDDEN,
  ),

  // General Memory Errors (7090-7099)
  MEMORY_OPERATION_FAILED: new ErrorCode(
    7090,
    'Thao tác memory thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MEMORY_VALIDATION_FAILED: new ErrorCode(
    7091,
    'Validation memory thất bại',
    HttpStatus.BAD_REQUEST,
  ),

  MEMORY_DATABASE_ERROR: new ErrorCode(
    7092,
    'Lỗi database khi xử lý memory',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  MEMORY_PERMISSION_DENIED: new ErrorCode(
    7093,
    'Không có quyền thực hiện thao tác này',
    HttpStatus.FORBIDDEN,
  ),

  MEMORY_LIMIT_EXCEEDED: new ErrorCode(
    7094,
    'Đã vượt quá giới hạn số lượng memory',
    HttpStatus.BAD_REQUEST,
  ),

  MEMORY_INVALID_FORMAT: new ErrorCode(
    7095,
    'Định dạng memory không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};


