import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import {
  AddPaymentGatewayToAgentDto,
  AgentPaymentGatewayResponseDto,
} from '../dto/agent/agent-payment-gateway.dto';
import { ErrorCode } from '@common/exceptions';
import { AgentPaymentGatewayService } from '../services';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { AGENT_ERROR_CODES } from '../../exceptions';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller quản lý payment gateway cho agent user
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT)
@Controller('v1/user/agents')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentPaymentGatewayController {
  constructor(
    private readonly agentPaymentGatewayService: AgentPaymentGatewayService,
  ) {}

  /**
   * Thêm payment gateway vào agent
   * @param agentId ID của agent
   * @param addDto Dữ liệu payment gateway
   * @param user Thông tin user từ JWT
   * @returns Thông báo thành công
   */
  @Post(':agentId/payment-gateway')
  @ApiOperation({
    summary: 'Thêm payment gateway vào agent',
    description: 'Liên kết payment gateway với agent. Yêu cầu agent có enableOutputToPayment = true',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    type: 'string',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Thêm payment gateway vào agent thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
    AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async addPaymentGateway(
    @Param('agentId') agentId: string,
    @Body() addDto: AddPaymentGatewayToAgentDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.agentPaymentGatewayService.addPaymentGateway(
      agentId,
      addDto.paymentGatewayId,
      userId,
    );
    return ApiResponseDto.created(null, 'Thêm payment gateway vào agent thành công');
  }

  /**
   * Lấy thông tin payment gateway của agent
   * @param agentId ID của agent
   * @param user Thông tin user từ JWT
   * @returns Thông tin payment gateway
   */
  @Get(':agentId/payment-gateway')
  @ApiOperation({
    summary: 'Lấy thông tin payment gateway của agent',
    description: 'Lấy thông tin chi tiết payment gateway được liên kết với agent',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    type: 'string',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin payment gateway thành công',
    type: AgentPaymentGatewayResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getPaymentGateway(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<AgentPaymentGatewayResponseDto | null>> {
    const paymentGateway = await this.agentPaymentGatewayService.getPaymentGateway(
      agentId,
      userId,
    );
    return ApiResponseDto.success(
      paymentGateway,
      paymentGateway ? 'Lấy thông tin payment gateway thành công' : 'Agent chưa có payment gateway',
    );
  }

  /**
   * Gỡ payment gateway khỏi agent
   * @param agentId ID của agent
   * @param user Thông tin user từ JWT
   * @returns Thông báo thành công
   */
  @Delete(':agentId/payment-gateway')
  @ApiOperation({
    summary: 'Gỡ payment gateway khỏi agent',
    description: 'Hủy liên kết payment gateway với agent',
  })
  @ApiParam({
    name: 'agentId',
    description: 'ID của agent',
    type: 'string',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gỡ payment gateway khỏi agent thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removePaymentGateway(
    @Param('agentId') agentId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.agentPaymentGatewayService.removePaymentGateway(agentId, userId);
    return ApiResponseDto.success(null, 'Gỡ payment gateway khỏi agent thành công');
  }
}
