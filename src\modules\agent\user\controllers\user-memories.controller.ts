import {
  Controller,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { UserMemoriesService } from '../services';
import {
  UpdateUserMemoryDto,
  QueryUserMemoryDto,
  UserMemoryResponseDto,
} from '../dto/user-memories';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API liên quan đến user memories
 */
@ApiTags(SWAGGER_API_TAGS.USER_MEMORIES)
@Controller('v1/user/memories')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserMemoriesController {
  constructor(private readonly userMemoriesService: UserMemoriesService) {}

  /**
   * Cập nhật user memory
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật user memory',
    description: 'Cập nhật thông tin của một user memory theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật memory thành công',
    type: ApiResponseDto<UserMemoryResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Memory không tồn tại hoặc không thuộc về user',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Chưa xác thực',
  })
  async updateUserMemory(
    @Param('id') memoryId: string,
    @Body() updateData: UpdateUserMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<UserMemoryResponseDto>> {
    const result = await this.userMemoriesService.updateUserMemory(
      memoryId,
      userId,
      updateData,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách user memories
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách user memories',
    description: 'Lấy danh sách memories của user với phân trang và filter',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (mặc định: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng items per page (mặc định: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Từ khóa tìm kiếm trong nội dung memory',
    example: 'âm nhạc',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Lọc theo loại memory',
    example: 'preference',
  })
  @ApiQuery({
    name: 'agentId',
    required: false,
    description: 'Lọc theo agent ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description: 'Lọc theo tags (array)',
    example: ['personal', 'preference'],
    type: [String],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách memories thành công',
    type: ApiResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Chưa xác thực',
  })
  async getUserMemoriesList(
    @Query() query: QueryUserMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.userMemoriesService.getUserMemoriesList(
      userId,
      query,
    );

    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách memories thành công',
    );
  }

  /**
   * Xóa user memory
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa user memory',
    description: 'Xóa một user memory theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa memory thành công',
    type: ApiResponseDto<boolean>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Memory không tồn tại hoặc không thuộc về user',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Chưa xác thực',
  })
  async deleteUserMemory(
    @Param('id') memoryId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userMemoriesService.deleteUserMemory(
      memoryId,
      userId,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
