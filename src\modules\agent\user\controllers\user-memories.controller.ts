import {
  Controller,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@common/response';
import { UserMemoriesService } from '../services';
import {
  UpdateUserMemoryDto,
  QueryUserMemoryDto,
  UserMemoryResponseDto,
} from '../dto/user-memories';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API liên quan đến user memories
 */
@ApiTags(SWAGGER_API_TAGS.USER_MEMORIES)
@Controller('v1/user/memories')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserMemoriesController {
  constructor(private readonly userMemoriesService: UserMemoriesService) {}

  /**
   * Cập nhật user memory
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật user memory',
    description: 'Cập nhật thông tin của một user memory theo ID. Chỉ có thể cập nhật memory thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật memory',
    type: UpdateUserMemoryDto,
    examples: {
      example1: {
        summary: 'Cập nhật memory cơ bản',
        description: 'Ví dụ cập nhật memory với structured content',
        value: {
          structuredContent: {
            title: 'Sở thích âm nhạc đã cập nhật',
            content: 'Người dùng thích nghe nhạc pop, rock và jazz',
            reason: 'Để cá nhân hóa trải nghiệm âm nhạc tốt hơn',
            type: 'preference',
            importance: 9,
            keywords: ['âm nhạc', 'pop', 'rock', 'jazz'],
            context: 'Khi đề xuất nhạc hoặc thảo luận về âm nhạc'
          },
          metadata: {
            source: 'user_input',
            tags: ['personal', 'preference', 'music'],
            lastUpdated: 1703123456789
          }
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Cập nhật memory thành công',
    type: ApiResponseDto<UserMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Cập nhật memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          userId: 12345,
          structuredContent: {
            title: 'Sở thích âm nhạc đã cập nhật',
            content: 'Người dùng thích nghe nhạc pop, rock và jazz',
            reason: 'Để cá nhân hóa trải nghiệm âm nhạc tốt hơn',
            type: 'preference',
            importance: 9,
            keywords: ['âm nhạc', 'pop', 'rock', 'jazz'],
            context: 'Khi đề xuất nhạc hoặc thảo luận về âm nhạc'
          },
          metadata: {
            source: 'user_input',
            tags: ['personal', 'preference', 'music'],
            lastUpdated: 1703123456789
          },
          createdAt: 1703120000000
        }
      }
    }
  })
  @ApiNotFoundResponse({
    description: 'Memory không tồn tại hoặc không thuộc về user',
    schema: {
      example: {
        success: false,
        message: 'Memory không tồn tại hoặc không thuộc về user này',
        errorCode: 7000,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiBadRequestResponse({
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Dữ liệu memory không hợp lệ',
        errorCode: 7002,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Chưa xác thực hoặc token không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Unauthorized',
        statusCode: 401,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  async updateUserMemory(
    @Param('id') memoryId: string,
    @Body() updateData: UpdateUserMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<UserMemoryResponseDto>> {
    const result = await this.userMemoriesService.updateUserMemory(
      memoryId,
      userId,
      updateData,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách user memories
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách user memories',
    description: 'Lấy danh sách memories của user với phân trang và các bộ lọc. Hỗ trợ tìm kiếm theo nội dung, lọc theo loại, agent ID và tags.',
  })
  @ApiOkResponse({
    description: 'Lấy danh sách memories thành công',
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách memories thành công',
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              userId: 12345,
              structuredContent: {
                title: 'Sở thích âm nhạc',
                content: 'Người dùng thích nghe nhạc pop và rock',
                reason: 'Để cá nhân hóa trải nghiệm âm nhạc',
              },
              metadata: {},
              createdAt: 1703120000000
            }
          ],
          meta: {
            totalItems: 25,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 3,
            currentPage: 1,
            hasItems: true
          }
        }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Chưa xác thực hoặc token không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Unauthorized',
        statusCode: 401,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  async getUserMemoriesList(
    @Query() query: QueryUserMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.userMemoriesService.getUserMemoriesList(
      userId,
      query,
    );

    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách memories thành công',
    );
  }

  /**
   * Xóa user memory
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa user memory',
    description: 'Xóa vĩnh viễn một user memory theo ID. Chỉ có thể xóa memory thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  
  async deleteUserMemory(
    @Param('id') memoryId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.userMemoriesService.deleteUserMemory(
      memoryId,
      userId,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
