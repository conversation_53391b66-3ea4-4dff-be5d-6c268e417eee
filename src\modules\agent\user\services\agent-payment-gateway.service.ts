import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AgentUserRepository } from '@modules/agent/repositories';
import { PaymentGatewayRepository, UserCompanyInSepayRepository } from '@modules/integration/repositories';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { AgentPaymentGatewayResponseDto } from '../dto/agent/agent-payment-gateway.dto';
import { Transactional } from 'typeorm-transactional';
import { AGENT_ERROR_CODES } from '../../exceptions';

/**
 * Service quản lý payment gateway cho agent user
 */
@Injectable()
export class AgentPaymentGatewayService {
  private readonly logger = new Logger(AgentPaymentGatewayService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly paymentGatewayRepository: PaymentGatewayRepository,
    private readonly userCompanyInSepayRepository: UserCompanyInSepayRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
  ) {}

  /**
   * Thêm payment gateway vào agent
   * @param agentId ID của agent
   * @param paymentGatewayId ID của payment gateway
   * @param userId ID của user
   */
  @Transactional()
  async addPaymentGateway(
    agentId: string,
    paymentGatewayId: number,
    userId: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Adding payment gateway ${paymentGatewayId} to agent ${agentId} for user ${userId}`,
      );

      // 1. Kiểm tra agent tồn tại và thuộc về user
      const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!agentData) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 2. Kiểm tra type agent có hỗ trợ payment không
      const typeAgent = await this.typeAgentRepository.findById(agentData.agentUser.typeId);
      if (!typeAgent || !typeAgent.config.enableOutputToPayment) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_OUTPUT_NOT_SUPPORTED,
          'Agent type không hỗ trợ tính năng thanh toán',
        );
      }

      // 3. Kiểm tra payment gateway tồn tại và thuộc về user
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { id: paymentGatewayId },
      });

      if (!paymentGateway) {
        throw new AppException(
          AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND,
          'Payment gateway không tồn tại',
        );
      }

      // Kiểm tra payment gateway thuộc về user (thông qua company_id)
      const userCompanyInSepay = await this.userCompanyInSepayRepository.findOne({
        where: { userId, companyId: paymentGateway.companyId.toString() }
      });

      if (!userCompanyInSepay) {
        throw new AppException(
          AGENT_ERROR_CODES.PAYMENT_GATEWAY_NOT_FOUND,
          'Payment gateway không thuộc về user này',
        );
      }

      // 4. Cập nhật payment gateway ID cho agent
      await this.agentUserRepository.updatePaymentGateway(agentId, paymentGatewayId);

      this.logger.log(
        `Successfully added payment gateway ${paymentGatewayId} to agent ${agentId}`,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error adding payment gateway to agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Lấy thông tin payment gateway của agent
   * @param agentId ID của agent
   * @param userId ID của user
   * @returns Thông tin payment gateway hoặc null
   */
  async getPaymentGateway(
    agentId: string,
    userId: number,
  ): Promise<AgentPaymentGatewayResponseDto | null> {
    try {
      this.logger.log(`Getting payment gateway for agent ${agentId} of user ${userId}`);

      // 1. Kiểm tra agent tồn tại và thuộc về user
      const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!agentData) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 2. Nếu agent không có payment gateway
      if (!agentData.agentUser.paymentGatewayId) {
        return null;
      }

      // 3. Lấy thông tin payment gateway
      const paymentGateway = await this.paymentGatewayRepository.findOne({
        where: { id: agentData.agentUser.paymentGatewayId },
      });

      if (!paymentGateway) {
        this.logger.warn(
          `Payment gateway ${agentData.agentUser.paymentGatewayId} not found for agent ${agentId}`,
        );
        return null;
      }

      // 4. Map sang DTO
      return this.mapToResponseDto(paymentGateway);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error getting payment gateway for agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Gỡ payment gateway khỏi agent
   * @param agentId ID của agent
   * @param userId ID của user
   */
  @Transactional()
  async removePaymentGateway(agentId: string, userId: number): Promise<void> {
    try {
      this.logger.log(`Removing payment gateway from agent ${agentId} for user ${userId}`);

      // 1. Kiểm tra agent tồn tại và thuộc về user
      const agentData = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);
      if (!agentData) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 2. Cập nhật payment gateway ID thành null
      await this.agentUserRepository.updatePaymentGateway(agentId, null);

      this.logger.log(`Successfully removed payment gateway from agent ${agentId}`);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing payment gateway from agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Map PaymentGateway entity sang response DTO
   * @param paymentGateway PaymentGateway entity
   * @returns AgentPaymentGatewayResponseDto
   */
  private mapToResponseDto(paymentGateway: any): AgentPaymentGatewayResponseDto {
    return {
      id: paymentGateway.id,
      accountId: paymentGateway.accountId,
      bankCode: paymentGateway.bankCode,
      accountNumber: paymentGateway.accountNumber,
      accountHolderName: paymentGateway.accountHolderName,
      label: paymentGateway.label,
      status: paymentGateway.status,
      merchantName: paymentGateway.merchantName,
      merchantAddress: paymentGateway.merchantAddress,
      isVa: paymentGateway.isVa || false,
      vaId: paymentGateway.vaId,
      canCreateVa: paymentGateway.canCreateVa || false,
    };
  }
}
