import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { MediaModule } from '@modules/data/media/media.module';
import { UrlModule } from '@modules/data/url/url.module';
import { MarketplaceModule } from '@modules/marketplace/marketplace.module';
import { KnowledgeFilesModule } from '@modules/data/knowledge-files/knowledge-files.module';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { MediaRepository } from '@modules/data/media/repositories';
import { UrlRepository } from '@modules/data/url/repositories';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { RagFileProcessingService } from '@shared/services/ai/rag-file-processing.service';
import { S3Service } from '@shared/services/s3.service';
import { FacebookService } from '@shared/services/facebook/facebook.service';
import { ToolsModule } from '@/modules/tools/tools.module';
import { ToolsUserModule } from '@/modules/tools/user/tools-user.module';

import { BusinessUserModule } from '@modules/business/user/business-user.module';
import { IntegrationModule } from '@modules/integration/integration.module';
import { UserWebsiteRepository, FacebookPageRepository } from '@modules/integration/repositories';
import { ZaloOfficialAccountRepository } from '@modules/marketing/user/repositories';
import { ZaloOfficialAccount } from '@modules/marketing/user/entities';
import { SystemModelsRepository } from '@modules/models/repositories/system-models.repository';
import {
  Agent,
  AgentMedia,
  AgentProduct,
  AgentUrl,
  AgentUser,
  AgentStrategyUser,
  TypeAgent,
  UserMultiAgent,
  AgentUserTools,
  AgentMemories,
  UserMemories
} from '@modules/agent/entities';
import {
  AgentRepository,
  AgentUserRepository,
  AgentStrategyRepository,
  AgentStrategyUserRepository,
  TypeAgentRepository,
  AgentMediaRepository,
  AgentProductRepository,
  AgentUrlRepository,
  AgentRankRepository,
  UserMultiAgentRepository,
  AgentUserToolsRepository,
  TypeAgentToolsRepository,
  TypeAgentModelsRepository,
  AgentMemoriesRepository,
  UserMemoriesRepository
} from '@modules/agent/repositories';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories';
import { PaymentGatewayRepository, UserCompanyInSepayRepository } from '@modules/integration/repositories';
import { AgentListMapper } from '@modules/agent/mappers/agent-list.mapper';
import {
  TypeAgentUserController,
  AgentResourceUserController,
  MultiAgentUserController,
  ProfileUserController,
  ConversionUserController,
  BasicInfoUserController,
  AgentStrategyController,
  AgentFacebookPageController,
  AgentWebsiteController,
  AgentUserController,
  AgentToolsUserController,
  AgentZaloController,
  AgentPaymentGatewayController,
  UserMemoriesController,
  AgentMemoriesController
} from './controllers';
import {
  AgentUserService,
  AgentResourceUserService,
  MultiAgentUserService,
  ProfileUserService,
  ConversionUserService,
  BasicInfoUserService,
  AgentStrategyService,
  AgentFacebookPageService,
  AgentWebsiteService,
  AgentToolsUserService,
  AgentZaloService,
  AgentPaymentGatewayService,
  AgentValidationService,
  UserMemoriesService,
  AgentMemoriesService
} from './services';
import { AgentStrategyUserService } from './services/agent-strategy-user.service';
import { TypeAgentUserService } from '@modules/agent/user/services';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([
      UserProduct,
      Agent,
      AgentUser,
      AgentStrategyUser,
      AgentMedia,
      AgentUrl,
      AgentProduct,
      TypeAgent,
      UserMultiAgent,
      AgentUserTools,
      ZaloOfficialAccount,
      AgentMemories,
      UserMemories,
    ]),
    HttpModule,
    MediaModule,
    UrlModule,
    MarketplaceModule,
    ToolsModule,
    ToolsUserModule,
    KnowledgeFilesModule,
    ConfigModule,
    BusinessUserModule,
    IntegrationModule,
  ],
  controllers: [
    TypeAgentUserController,
    AgentResourceUserController,
    MultiAgentUserController,
    ProfileUserController,
    ConversionUserController,
    BasicInfoUserController,
    AgentStrategyController,
    AgentFacebookPageController,
    AgentWebsiteController,
    AgentUserController,
    AgentToolsUserController,
    AgentZaloController,
    AgentPaymentGatewayController,
    UserMemoriesController,
    AgentMemoriesController,
  ],
  providers: [
    // Services
    TypeAgentUserService,
    AgentUserService,
    AgentResourceUserService,
    MultiAgentUserService,
    ProfileUserService,
    ConversionUserService,
    BasicInfoUserService,
    AgentStrategyService,
    AgentStrategyUserService,
    AgentFacebookPageService,
    AgentWebsiteService,
    AgentToolsUserService,
    AgentZaloService,
    AgentValidationService,
    AgentPaymentGatewayService,
    UserMemoriesService,
    AgentMemoriesService,

    // External services
    OpenAiService,
    S3Service,
    FacebookService,
    RagFileProcessingService,

    // Mappers
    AgentListMapper,

    // Repositories
    TypeAgentRepository,
    AgentRepository,
    AgentUserRepository,
    AgentStrategyRepository,
    AgentStrategyUserRepository,
    AgentMediaRepository,
    AgentProductRepository,
    AgentUrlRepository,
    VectorStoreRepository,
    MediaRepository,
    UrlRepository,
    UserWebsiteRepository,
    FacebookPageRepository,
    AgentRankRepository,
    UserMultiAgentRepository,
    AgentUserToolsRepository,
    ZaloOfficialAccountRepository,
    SystemModelsRepository,
    TypeAgentToolsRepository,
    TypeAgentModelsRepository,
    PaymentGatewayRepository,
    UserCompanyInSepayRepository,
    AgentMemoriesRepository,
    UserMemoriesRepository,
  ],
  exports: [
    AgentUserService,
    AgentResourceUserService,
    AgentStrategyService,
    UserMemoriesService,
    AgentMemoriesService,
  ],
})
export class AgentUserModule {}
