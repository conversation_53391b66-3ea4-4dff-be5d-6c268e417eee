import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { UserMemories } from '@modules/agent/entities';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';
import { UserMemoriesRepository } from '@modules/agent/repositories';
import { Injectable, Logger } from '@nestjs/common';
import {
  CreateUserMemoryDto,
  QueryUserMemoryDto,
  UpdateUserMemoryDto,
  UserMemoryResponseDto
} from '../dto/user-memories';

/**
 * Service xử lý logic nghiệp vụ cho user memories
 */
@Injectable()
export class UserMemoriesService {
  private readonly logger = new Logger(UserMemoriesService.name);

  constructor(
    private readonly userMemoriesRepository: UserMemoriesRepository,
  ) { }

  /**
   * Tạo user memory mới
   * @param userId ID của người dùng
   * @param createData Dữ liệu tạo memory
   * @returns UserMemoryResponseDto
   */
  async createUserMemory(
    userId: number,
    createData: CreateUserMemoryDto,
  ): Promise<UserMemoryResponseDto> {
    try {
      this.logger.log(`Creating user memory for user ${userId}`);

      // Tạo memory mới
      const newMemory = await this.userMemoriesRepository.createMemory(
        userId,
        createData.structuredContent,
        createData.metadata,
      );

      this.logger.log(`Successfully created user memory ${newMemory.id} for user ${userId}`);

      return this.mapToResponseDto(newMemory);
    } catch (error) {
      this.logger.error(`Error creating user memory for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED, 'Lỗi khi tạo memory');
    }
  }

  /**
   * Cập nhật user memory
   * @param memoryId ID của memory cần cập nhật
   * @param userId ID của người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns UserMemoryResponseDto
   */
  async updateUserMemory(
    memoryId: string,
    userId: number,
    updateData: UpdateUserMemoryDto,
  ): Promise<{ id: string }> {
    try {
      this.logger.log(`Updating user memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại và thuộc về user không
      const existingMemory = await this.userMemoriesRepository.findOne({
        where: { id: memoryId, userId },
      });

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_NOT_FOUND, 'Memory không tồn tại hoặc không thuộc về user này');
      }

      // Cập nhật memory
      const success = await this.userMemoriesRepository.updateMemory(
        memoryId,
        userId,
        updateData.structuredContent,
      );

      if (!success) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_UPDATE_FAILED, 'Không thể cập nhật memory');
      }

      this.logger.log(`Successfully updated user memory ${memoryId} for user ${userId}`);

      return { id: existingMemory.id };
    } catch (error) {
      this.logger.error(`Error updating user memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED, 'Lỗi khi cập nhật memory');
    }
  }

  /**
   * Lấy danh sách user memories với phân trang
   * @param userId ID của người dùng
   * @param query Tham số query
   * @returns PaginatedResult<UserMemoryResponseDto>
   */
  async getUserMemoriesList(
    userId: number,
    query: QueryUserMemoryDto,
  ): Promise<PaginatedResult<UserMemoryResponseDto>> {
    try {
      this.logger.log(`Getting user memories list for user ${userId}`);

      let memories: PaginatedResult<UserMemories>;
      if (query.search) {
        const memoriesList = await this.userMemoriesRepository.searchMemories(
          userId,
          query.search,
          query.limit,
        );

        memories = {
          items: memoriesList,
          meta: {
            totalItems: memoriesList.length,
            itemCount: memoriesList.length,
            itemsPerPage: query.limit,
            totalPages: Math.ceil(memoriesList.length / query.limit),
            currentPage: query.page,
            hasItems: memoriesList.length > 0,
          },
        };
      }
      // Lấy tất cả với phân trang
      else {
        memories = await this.userMemoriesRepository.findByUserIdWithPagination(
          userId,
          query.page,
          query.limit,
          query.search,
        );
      }

      const responseItems = memories.items.map(memory => this.mapToResponseDto(memory));

      this.logger.log(`Successfully retrieved ${responseItems.length} user memories for user ${userId}`);

      return {
        ...memories,
        items: responseItems,
      };
    } catch (error) {
      this.logger.error(`Error getting user memories list for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED, 'Lỗi khi lấy danh sách memories');
    }
  }

  /**
   * Xóa user memory
   * @param memoryId ID của memory cần xóa
   * @param userId ID của người dùng
   * @returns boolean
   */
  async deleteUserMemory(memoryId: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Deleting user memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại và thuộc về user không
      const existingMemory = await this.userMemoriesRepository.findOne({
        where: { id: memoryId, userId },
      });

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_NOT_FOUND, 'Memory không tồn tại hoặc không thuộc về user này');
      }

      // Xóa memory
      const success = await this.userMemoriesRepository.deleteMemory(memoryId, userId);

      if (!success) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_DELETE_FAILED, 'Không thể xóa memory');
      }

      this.logger.log(`Successfully deleted user memory ${memoryId} for user ${userId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting user memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED, 'Lỗi khi xóa memory');
    }
  }

  /**
   * Map UserMemories entity sang UserMemoryResponseDto
   * @param memory UserMemories entity
   * @returns UserMemoryResponseDto
   */
  private mapToResponseDto(memory: UserMemories): UserMemoryResponseDto {
    return {
      id: memory.id,
      structuredContent: memory.structuredContent,
      createdAt: memory.createdAt,
    };
  }
}
