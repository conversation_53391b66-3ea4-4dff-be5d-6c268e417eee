import { Injectable, Logger } from '@nestjs/common';
import { UserMemoriesRepository } from '@modules/agent/repositories';
import {
  UpdateUserMemoryDto,
  QueryUserMemoryDto,
  UserMemoryResponseDto
} from '../dto/user-memories';
import { PaginatedResult } from '@common/response';
import { AppException } from '@common/exceptions';
import { UserMemories } from '@modules/agent/entities';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';

/**
 * Service xử lý logic nghiệp vụ cho user memories
 */
@Injectable()
export class UserMemoriesService {
  private readonly logger = new Logger(UserMemoriesService.name);

  constructor(
    private readonly userMemoriesRepository: UserMemoriesRepository,
  ) {}

  /**
   * Cập nhật user memory
   * @param memoryId ID của memory cần cập nhật
   * @param userId ID của người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns UserMemoryResponseDto
   */
  async updateUserMemory(
    memoryId: string,
    userId: number,
    updateData: UpdateUserMemoryDto,
  ): Promise<UserMemoryResponseDto> {
    try {
      this.logger.log(`Updating user memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại và thuộc về user không
      const existingMemory = await this.userMemoriesRepository.findOne({
        where: { id: memoryId, userId },
      });

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_NOT_FOUND, 'Memory không tồn tại hoặc không thuộc về user này');
      }

      // Cập nhật memory
      const success = await this.userMemoriesRepository.updateMemory(
        memoryId,
        userId,
        updateData.structuredContent,
        updateData.metadata,
      );

      if (!success) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_UPDATE_FAILED, 'Không thể cập nhật memory');
      }

      // Lấy memory đã cập nhật
      const updatedMemory = await this.userMemoriesRepository.findOne({
        where: { id: memoryId, userId },
      });

      if (!updatedMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_NOT_FOUND, 'Không thể lấy memory sau khi cập nhật');
      }

      this.logger.log(`Successfully updated user memory ${memoryId} for user ${userId}`);

      return this.mapToResponseDto(updatedMemory);
    } catch (error) {
      this.logger.error(`Error updating user memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED, 'Lỗi khi cập nhật memory');
    }
  }

  /**
   * Lấy danh sách user memories với phân trang
   * @param userId ID của người dùng
   * @param query Tham số query
   * @returns PaginatedResult<UserMemoryResponseDto>
   */
  async getUserMemoriesList(
    userId: number,
    query: QueryUserMemoryDto,
  ): Promise<PaginatedResult<UserMemoryResponseDto>> {
    try {
      this.logger.log(`Getting user memories list for user ${userId}`);

      let memories: PaginatedResult<UserMemories>;

      // Nếu có filter theo agentId
      if (query.agentId) {
        const memoriesList = await this.userMemoriesRepository.findByUserIdAndAgentId(
          userId,
          query.agentId,
          query.limit,
        );
        
        memories = {
          items: memoriesList,
          meta: {
            totalItems: memoriesList.length,
            itemCount: memoriesList.length,
            itemsPerPage: query.limit,
            totalPages: Math.ceil(memoriesList.length / query.limit),
            currentPage: query.page,
            hasItems: memoriesList.length > 0,
          },
        };
      } 
      // Nếu có filter theo type
      else if (query.type) {
        const memoriesList = await this.userMemoriesRepository.findByUserIdAndType(
          userId,
          query.type,
          query.limit,
        );
        
        memories = {
          items: memoriesList,
          meta: {
            totalItems: memoriesList.length,
            itemCount: memoriesList.length,
            itemsPerPage: query.limit,
            totalPages: Math.ceil(memoriesList.length / query.limit),
            currentPage: query.page,
            hasItems: memoriesList.length > 0,
          },
        };
      }
      // Nếu có search
      else if (query.search) {
        const memoriesList = await this.userMemoriesRepository.searchMemories(
          userId,
          query.search,
          query.limit,
        );
        
        memories = {
          items: memoriesList,
          meta: {
            totalItems: memoriesList.length,
            itemCount: memoriesList.length,
            itemsPerPage: query.limit,
            totalPages: Math.ceil(memoriesList.length / query.limit),
            currentPage: query.page,
            hasItems: memoriesList.length > 0,
          },
        };
      }
      // Lấy tất cả với phân trang
      else {
        memories = await this.userMemoriesRepository.findByUserIdWithPagination(
          userId,
          query.page,
          query.limit,
          query.search,
        );
      }

      const responseItems = memories.items.map(memory => this.mapToResponseDto(memory));

      this.logger.log(`Successfully retrieved ${responseItems.length} user memories for user ${userId}`);

      return {
        ...memories,
        items: responseItems,
      };
    } catch (error) {
      this.logger.error(`Error getting user memories list for user ${userId}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED, 'Lỗi khi lấy danh sách memories');
    }
  }

  /**
   * Xóa user memory
   * @param memoryId ID của memory cần xóa
   * @param userId ID của người dùng
   * @returns boolean
   */
  async deleteUserMemory(memoryId: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Deleting user memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại và thuộc về user không
      const existingMemory = await this.userMemoriesRepository.findOne({
        where: { id: memoryId, userId },
      });

      if (!existingMemory) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_NOT_FOUND, 'Memory không tồn tại hoặc không thuộc về user này');
      }

      // Xóa memory
      const success = await this.userMemoriesRepository.deleteMemory(memoryId, userId);

      if (!success) {
        throw new AppException(MEMORIES_ERROR_CODES.USER_MEMORY_DELETE_FAILED, 'Không thể xóa memory');
      }

      this.logger.log(`Successfully deleted user memory ${memoryId} for user ${userId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting user memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException(MEMORIES_ERROR_CODES.MEMORY_OPERATION_FAILED, 'Lỗi khi xóa memory');
    }
  }

  /**
   * Map UserMemories entity sang UserMemoryResponseDto
   * @param memory UserMemories entity
   * @returns UserMemoryResponseDto
   */
  private mapToResponseDto(memory: UserMemories): UserMemoryResponseDto {
    return {
      id: memory.id,
      userId: memory.userId,
      structuredContent: memory.structuredContent,
      metadata: memory.metadata || undefined,
      createdAt: memory.createdAt,
    };
  }
}
