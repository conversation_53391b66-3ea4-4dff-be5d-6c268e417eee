/**
 * Interface cho structured_content của user memories
 * Đ<PERSON><PERSON> nghĩa cấu trúc nội dung kiến thức về người dùng
 */
export interface UserMemoryStructuredContent {
  /**
   * Tiêu đề của memory
   */
  title?: string;

  /**
   * L<PERSON> do ghi nhớ thông tin này
   */
  reason?: string;

  /**
   * Nội dung chính của memory
   */
  content: string;

  /**
   * Loại memory (personal_info, preference, behavior, etc.)
   */
  type?: string;

  /**
   * Mức độ quan trọng (1-10)
   */
  importance?: number;

  /**
   * Các từ khóa liên quan
   */
  keywords?: string[];

  /**
   * Ngữ cảnh sử dụng
   */
  context?: string;
}

/**
 * Interface cho metadata của user memories
 * Chứa thông tin bổ sung về memory
 */
export interface UserMemoryMetadata {
  /**
   * ID của agent tạo ra memory này
   */
  agentId?: string;

  /**
   * <PERSON>uồn gốc của thông tin
   */
  source?: string;

  /**
   * Thời gian cập nhật cuối
   */
  lastUpdated?: number;

  /**
   * Số lần sử dụng memory
   */
  usageCount?: number;

  /**
   * Tags để phân loại
   */
  tags?: string[];

  /**
   * Thông tin bổ sung khác
   */
  [key: string]: any;
}
