import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Interface cho structured_content của user memories
 * Định nghĩa cấu trúc nội dung kiến thức về người dùng
 */
export interface UserMemoryStructuredContent {
  /**
   * Tiêu đề của memory
   */
  title?: string;

  /**
   * Lý do ghi nhớ thông tin này
   */
  reason?: string;

  /**
   * Nội dung chính của memory
   */
  content: string;

  /**
   * Loại memory (personal_info, preference, behavior, etc.)
   */
  type?: string;

  /**
   * Mức độ quan trọng (1-10)
   */
  importance?: number;

  /**
   * Các từ khóa liên quan
   */
  keywords?: string[];

  /**
   * Ngữ cảnh sử dụng
   */
  context?: string;
}

/**
 * Interface cho metadata của user memories
 * Chứa thông tin bổ sung về memory
 */
export interface UserMemoryMetadata {
  /**
   * ID của agent tạo ra memory này
   */
  agentId?: string;

  /**
   * <PERSON>uồn gốc của thông tin
   */
  source?: string;

  /**
   * Thời gian cập nhật cuối
   */
  lastUpdated?: number;

  /**
   * Số lần sử dụng memory
   */
  usageCount?: number;

  /**
   * Tags để phân loại
   */
  tags?: string[];

  /**
   * Thông tin bổ sung khác
   */
  [key: string]: any;
}

/**
 * Entity đại diện cho bảng user_memories trong cơ sở dữ liệu
 * Lưu trữ các facts có cấu trúc về người dùng để xây dựng hồ sơ kiến thức
 */
@Entity('user_memories')
export class UserMemories {
  /**
   * UUID định danh duy nhất cho mỗi memory
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID của người dùng sở hữu memory
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Nội dung fact dưới dạng JSON (ví dụ: title, summary, source)
   */
  @Column({ name: 'structured_content', type: 'jsonb' })
  structuredContent: UserMemoryStructuredContent;

  /**
   * Metadata bổ sung cho memory
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: UserMemoryMetadata | null;

  /**
   * Thời điểm tạo memory (timestamp milliseconds)
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)'
  })
  createdAt: number;
}
