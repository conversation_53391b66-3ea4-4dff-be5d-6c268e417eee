import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { UserMemoryMetadata, UserMemoryStructuredContent } from '../interfaces/user-memories.interface';

/**
 * Entity đại diện cho bảng user_memories trong cơ sở dữ liệu
 * Lưu trữ các facts có cấu trúc về người dùng để xây dựng hồ sơ kiến thức
 */
@Entity('user_memories')
export class UserMemories {
  /**
   * UUID định danh duy nhất cho mỗi memory
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID của người dùng sở hữu memory
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Nội dung fact dưới dạng JSON (ví dụ: title, summary, source)
   */
  @Column({ name: 'structured_content', type: 'jsonb' })
  structuredContent: UserMemoryStructuredContent;

  /**
   * Metadata bổ sung cho memory
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: UserMemoryMetadata | null;

  /**
   * Thời điểm tạo memory (timestamp milliseconds)
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)'
  })
  createdAt: number;
}
