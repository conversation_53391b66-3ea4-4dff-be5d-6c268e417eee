import { Injectable } from '@nestjs/common';
import { AgentMemories } from '@modules/agent/entities';
import { AgentMemoryResponseDto, AgentMemoryStructuredContentDto, AgentMemoryMetadataDto } from '../dto/agent-memories';

/**
 * Mapper để convert AgentMemories entity sang DTO
 */
@Injectable()
export class AgentMemoriesMapper {
  /**
   * Convert AgentMemories entity sang AgentMemoryResponseDto
   * @param entity AgentMemories entity
   * @returns AgentMemoryResponseDto
   */
  static toResponseDto(entity: AgentMemories): AgentMemoryResponseDto {
    return {
      id: entity.id,
      agentId: entity.agentId,
      structuredContent: entity.structuredContent, // Type assertion để tránh lỗi type
      createdAt: entity.createdAt || Date.now(),
    };
  }

  /**
   * Convert array AgentMemories entities sang array AgentMemoryResponseDto
   * @param entities Array AgentMemories entities
   * @returns Array AgentMemoryResponseDto
   */
  static toResponseDtoArray(entities: AgentMemories[]): AgentMemoryResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity));
  }

  /**
   * Convert AgentMemories entity sang simplified DTO (chỉ thông tin cơ bản)
   * @param entity AgentMemories entity
   * @returns Simplified DTO
   */
  static toSimpleDto(entity: AgentMemories) {
    return {
      id: entity.id,
      agentId: entity.agentId,
      title: entity.structuredContent.title || 'Không có tiêu đề',
      content: entity.structuredContent.content || 'Không có nội dung',
      reason: entity.structuredContent.reason || 'Không có lý do',
      createdAt: entity.createdAt || Date.now(),
    };
  }

  /**
   * Convert array AgentMemories entities sang array simplified DTOs
   * @param entities Array AgentMemories entities
   * @returns Array simplified DTOs
   */
  static toSimpleDtoArray(entities: AgentMemories[]) {
    return entities.map(entity => this.toSimpleDto(entity));
  }

  /**
   * Convert AgentMemories entity sang DTO với thông tin agent
   * @param entity AgentMemories entity
   * @param agentName Tên agent (optional)
   * @returns DTO với thông tin agent
   */
  static toResponseDtoWithAgent(entity: AgentMemories, agentName?: string) {
    return {
      ...this.toResponseDto(entity),
      agentName: agentName || 'Unknown Agent',
    };
  }
}
