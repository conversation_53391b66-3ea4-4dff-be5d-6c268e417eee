import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { 
  IsString, 
  IsOptional, 
  IsObject, 
  ValidateNested, 
  IsUUID,
  IsArray
} from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';
import { StructuredContentDto, MetadataDto } from '@modules/agent/dto/agent-memories.dto';

/**
 * DTO để cập nhật agent memory
 */
export class UpdateAgentMemoryDto {
  @ApiPropertyOptional({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: StructuredContentDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => StructuredContentDto)
  structuredContent?: StructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: MetadataDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => MetadataDto)
  metadata?: MetadataDto;
}

/**
 * DTO cho query danh sách agent memories
 */
export class QueryAgentMemoryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo agent ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  agentId?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo loại memory',
    example: 'skill',
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo mức độ quan trọng tối thiểu',
    example: 5,
  })
  @IsOptional()
  @Type(() => Number)
  minImportance?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo tags',
    example: ['javascript', 'programming'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

/**
 * DTO response cho agent memory
 */
export class AgentMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID của agent',
    example: '123e4567-e89b-12d3-a456-************',
  })
  agentId: string;

  @ApiProperty({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: StructuredContentDto,
  })
  structuredContent: StructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: MetadataDto,
  })
  metadata?: MetadataDto;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1703123456789,
  })
  createdAt: number;
}

/**
 * DTO response cho danh sách agent memories
 */
export class AgentMemoryListResponseDto {
  @ApiProperty({
    description: 'Danh sách memories',
    type: [AgentMemoryResponseDto],
  })
  items: AgentMemoryResponseDto[];

  @ApiProperty({
    description: 'Tổng số memories',
    example: 25,
  })
  total: number;

  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Số lượng items per page',
    example: 10,
  })
  limit: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 3,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Có items hay không',
    example: true,
  })
  hasItems: boolean;
}
