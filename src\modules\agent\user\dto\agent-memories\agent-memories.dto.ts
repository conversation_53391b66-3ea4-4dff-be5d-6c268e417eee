import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsObject,
  ValidateNested,
  IsUUID,
  IsArray
} from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';
import { StructuredContentInterface } from '@modules/agent/interfaces/agent-memory.interface';

/**
 * DTO cho structured content của agent memory
 */
export class AgentMemoryStructuredContentDto implements StructuredContentInterface {
  @ApiPropertyOptional({
    description: 'Tiêu đề của memory',
    example: 'JavaScript Programming',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Lý do ghi nhớ thông tin này',
    example: 'Để hỗ trợ lập trình',
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiProperty({
    description: 'Nội dung chính của memory',
    example: '<PERSON><PERSON><PERSON> thức về JavaScript cơ bản và nâng cao',
  })
  @IsString()
  content: string;
}

/**
 * DTO cho metadata của agent memory
 */
export class AgentMemoryMetadataDto {
  @ApiPropertyOptional({
    description: 'Nguồn gốc của thông tin',
    example: 'training',
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1703123456789,
  })
  @IsOptional()
  lastUpdated?: number;

  @ApiPropertyOptional({
    description: 'Tags để phân loại',
    example: ['programming', 'javascript'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  /**
   * Thông tin bổ sung khác
   */
  [key: string]: any;
}

/**
 * DTO để cập nhật agent memory
 */
export class UpdateAgentMemoryDto {
  @ApiPropertyOptional({
    description: 'Nội dung kiến thức dưới dạng JSON',
    type: AgentMemoryStructuredContentDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AgentMemoryStructuredContentDto)
  structuredContent?: AgentMemoryStructuredContentDto;

  @ApiPropertyOptional({
    description: 'Thông tin metadata bổ sung',
    type: AgentMemoryMetadataDto,
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AgentMemoryMetadataDto)
  metadata?: AgentMemoryMetadataDto;
}

/**
 * DTO cho query danh sách agent memories
 */
export class QueryAgentMemoryDto extends QueryDto {
}

/**
 * DTO response cho agent memory
 */
export class AgentMemoryResponseDto {
  @ApiProperty({
    description: 'UUID của memory',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'UUID của agent',
    example: '123e4567-e89b-12d3-a456-************',
  })
  agentId: string;
}

/**
 * DTO response cho danh sách agent memories
 */
export class AgentMemoryListResponseDto {
  @ApiProperty({
    description: 'Danh sách memories',
    type: [AgentMemoryResponseDto],
  })
  items: AgentMemoryResponseDto[];

  @ApiProperty({
    description: 'Metadata phân trang',
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}
