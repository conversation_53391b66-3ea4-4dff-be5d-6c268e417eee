import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { PaginatedResult } from '@common/response';
import { UserMemories } from '@modules/agent/entities';
import { StructuredContentInterface } from '@modules/agent/interfaces/agent-memory.interface';

/**
 * Repository cho UserMemories
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến user memories
 */
@Injectable()
export class UserMemoriesRepository extends Repository<UserMemories> {
  private readonly logger = new Logger(UserMemoriesRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserMemories, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserMemories
   * @returns SelectQueryBuilder cho UserMemories
   */
  createBaseQuery(): SelectQueryBuilder<UserMemories> {
    return this.createQueryBuilder('userMemories');
  }

  /**
   * Tìm memory theo ID và userId
   * @param id ID của memory
   * @param userId ID của người dùng
   * @returns UserMemories nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdAndUserId(id: string, userId: number): Promise<UserMemories | null> {
    try {
      return await this.createBaseQuery()
        .where('userMemories.id = :id', { id })
        .andWhere('userMemories.userId = :userId', { userId })
        .getOne();
    } catch (error) {
      this.logger.error(`Error finding memory ${id} for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách memories của user với phân trang
   * @param userId ID của người dùng
   * @param page Trang hiện tại
   * @param limit Số lượng items per page
   * @param search Từ khóa tìm kiếm (optional)
   * @returns Danh sách memories với phân trang
   */
  async findByUserIdWithPagination(
    userId: number,
    page: number = 1,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedResult<UserMemories>> {
    try {
      const queryBuilder = this.createBaseQuery()
        .where('userMemories.userId = :userId', { userId })
        .orderBy('userMemories.createdAt', 'DESC');

      // Thêm điều kiện tìm kiếm nếu có
      if (search && search.trim()) {
        queryBuilder.andWhere(
          '("userMemories"."structured_content"::text ILIKE :search OR "userMemories"."metadata"::text ILIKE :search)',
          { search: `%${search.trim()}%` }
        );
      }

      // Tính toán offset
      const offset = (page - 1) * limit;

      // Lấy tổng số records
      const totalItems = await queryBuilder.getCount();

      // Lấy data với phân trang
      const items = await queryBuilder
        .skip(offset)
        .take(limit)
        .getMany();

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(totalItems / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        items,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
          hasItems: totalItems > 0,
        },
      };
    } catch (error) {
      this.logger.error(`Error getting memories for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm memories theo loại (type trong structured_content)
   * @param userId ID của người dùng
   * @param type Loại memory
   * @param limit Giới hạn số lượng kết quả
   * @returns Danh sách memories
   */
  async findByUserIdAndType(
    userId: number,
    type: string,
    limit: number = 50
  ): Promise<UserMemories[]> {
    try {
      return await this.createBaseQuery()
        .where('userMemories.userId = :userId', { userId })
        .andWhere('"userMemories"."structured_content"->>\'type\' = :type', { type })
        .orderBy('userMemories.createdAt', 'DESC')
        .limit(limit)
        .getMany();
    } catch (error) {
      this.logger.error(`Error finding memories by type ${type} for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm memories theo agent ID
   * @param userId ID của người dùng
   * @param agentId ID của agent
   * @param limit Giới hạn số lượng kết quả
   * @returns Danh sách memories
   */
  async findByUserIdAndAgentId(
    userId: number,
    agentId: string,
    limit: number = 50
  ): Promise<UserMemories[]> {
    try {
      return await this.createBaseQuery()
        .where('userMemories.userId = :userId', { userId })
        .andWhere('"userMemories"."metadata"->>\'agentId\' = :agentId', { agentId })
        .orderBy('userMemories.createdAt', 'DESC')
        .limit(limit)
        .getMany();
    } catch (error) {
      this.logger.error(`Error finding memories by agent ${agentId} for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo memory mới
   * @param userId ID của người dùng
   * @param structuredContent Nội dung có cấu trúc
   * @param metadata Metadata (optional)
   * @returns UserMemories đã tạo
   */
  async createMemory(
    userId: number,
    structuredContent: StructuredContentInterface,
    metadata?: Record<string, any>
  ): Promise<UserMemories> {
    try {
      const memory = this.create({
        userId,
        structuredContent,
        metadata: metadata || {},
      });

      const savedMemory = await this.save(memory);
      this.logger.log(`Created memory ${savedMemory.id} for user ${userId}`);
      
      return savedMemory;
    } catch (error) {
      this.logger.error(`Error creating memory for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật memory
   * @param id ID của memory
   * @param userId ID của người dùng
   * @param structuredContent Nội dung có cấu trúc mới
   * @param metadata Metadata mới (optional)
   * @returns true nếu cập nhật thành công
   */
  async updateMemory(
    id: string,
    userId: number,
    structuredContent?: StructuredContentInterface,
    metadata?: Record<string, any>
  ): Promise<boolean> {
    try {
      const updateData: any = {};
      
      if (structuredContent) {
        updateData.structuredContent = structuredContent;
      }
      
      if (metadata !== undefined) {
        updateData.metadata = metadata;
      }

      const result = await this.createQueryBuilder()
        .update(UserMemories)
        .set(updateData)
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .execute();

      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      
      if (success) {
        this.logger.log(`Updated memory ${id} for user ${userId}`);
      }
      
      return success;
    } catch (error) {
      this.logger.error(`Error updating memory ${id} for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Xóa memory
   * @param id ID của memory
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async deleteMemory(id: string, userId: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(UserMemories)
        .where('id = :id', { id })
        .andWhere('userId = :userId', { userId })
        .execute();

      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      
      if (success) {
        this.logger.log(`Deleted memory ${id} for user ${userId}`);
      }
      
      return success;
    } catch (error) {
      this.logger.error(`Error deleting memory ${id} for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số lượng memories của user
   * @param userId ID của người dùng
   * @returns Số lượng memories
   */
  async countByUserId(userId: number): Promise<number> {
    try {
      return await this.createBaseQuery()
        .where('userMemories.userId = :userId', { userId })
        .getCount();
    } catch (error) {
      this.logger.error(`Error counting memories for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm kiếm memories theo nội dung
   * @param userId ID của người dùng
   * @param searchTerm Từ khóa tìm kiếm
   * @param limit Giới hạn số lượng kết quả
   * @returns Danh sách memories
   */
  async searchMemories(
    userId: number,
    searchTerm: string,
    limit: number = 20
  ): Promise<UserMemories[]> {
    try {
      return await this.createBaseQuery()
        .where('userMemories.userId = :userId', { userId })
        .andWhere(
          '("userMemories"."structured_content"::text ILIKE :search OR "userMemories"."metadata"::text ILIKE :search)',
          { search: `%${searchTerm}%` }
        )
        .orderBy('userMemories.createdAt', 'DESC')
        .limit(limit)
        .getMany();
    } catch (error) {
      this.logger.error(`Error searching memories for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
