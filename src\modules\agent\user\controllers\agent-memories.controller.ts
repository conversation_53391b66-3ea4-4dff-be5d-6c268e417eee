import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto } from '@common/response';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiBody,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import {
  AgentMemoryResponseDto,
  QueryAgentMemoryDto,
  UpdateAgentMemoryDto,
} from '../dto/agent-memories';
import { AgentMemoriesService } from '../services';

/**
 * Controller xử lý các API liên quan đến agent memories
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT_MEMORIES)
@Controller('v1/user/agent-memories')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentMemoriesController {
  constructor(private readonly agentMemoriesService: AgentMemoriesService) { }

  /**
   * Cập nhật agent memory
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật agent memory',
    description: 'Cập nhật thông tin của một agent memory theo ID. Chỉ có thể cập nhật memory của agent thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật agent memory',
    type: UpdateAgentMemoryDto,
    examples: {
      example1: {
        summary: 'Cập nhật memory kỹ năng',
        description: 'Ví dụ cập nhật memory về kỹ năng lập trình',
        value: {
          structuredContent: {
            title: 'JavaScript Programming Advanced',
            content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
            reason: 'Để hỗ trợ lập trình web hiệu quả hơn'
          },
          metadata: {
            source: 'training',
            tags: ['programming', 'javascript', 'web'],
            lastUpdated: 1703123456789
          }
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Cập nhật memory thành công',
    type: ApiResponseDto<AgentMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Cập nhật memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          agentId: '456e7890-e89b-12d3-a456-************',
          structuredContent: {
            title: 'JavaScript Programming Advanced',
            content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
            reason: 'Để hỗ trợ lập trình web hiệu quả hơn'
          },
          metadata: {
            source: 'training',
            tags: ['programming', 'javascript', 'web'],
            lastUpdated: 1703123456789
          },
          createdAt: 1703120000000
        }
      }
    }
  })
  @ApiNotFoundResponse({
    description: 'Memory không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Agent memory không tồn tại',
        errorCode: 7100,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiForbiddenResponse({
    description: 'Không có quyền truy cập agent này',
    schema: {
      example: {
        success: false,
        message: 'Bạn không có quyền truy cập agent này',
        errorCode: 7101,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiBadRequestResponse({
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Dữ liệu agent memory không hợp lệ',
        errorCode: 7102,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Chưa xác thực hoặc token không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Unauthorized',
        statusCode: 401,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  async updateAgentMemory(
    @Param('id') memoryId: string,
    @Body() updateData: UpdateAgentMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<AgentMemoryResponseDto>> {
    const result = await this.agentMemoriesService.updateAgentMemory(
      memoryId,
      userId,
      updateData,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách agent memories
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent memories',
    description: 'Lấy danh sách memories của tất cả agents thuộc về user với phân trang và các bộ lọc nâng cao. Hỗ trợ tìm kiếm theo nội dung, lọc theo agent cụ thể, loại memory và tags.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (mặc định: 1)',
    example: 1,
    type: 'number',
    minimum: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng items per page (mặc định: 10, tối đa: 100)',
    example: 10,
    type: 'number',
    minimum: 1,
    maximum: 100,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Từ khóa tìm kiếm trong nội dung memory (title, content, reason)',
    example: 'javascript programming',
    type: 'string',
  })
  @ApiQuery({
    name: 'agentId',
    required: false,
    description: 'Lọc theo agent ID cụ thể',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Lọc theo loại memory',
    example: 'skill',
    type: 'string',
    enum: ['skill', 'knowledge', 'experience', 'general'],
  })
  @ApiQuery({
    name: 'minImportance',
    required: false,
    description: 'Lọc theo mức độ quan trọng tối thiểu (1-10)',
    example: 5,
    type: 'number',
    minimum: 1,
    maximum: 10,
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description: 'Lọc theo tags (có thể truyền nhiều giá trị)',
    example: ['javascript', 'programming', 'web'],
    type: [String],
    isArray: true,
  })
  @ApiOkResponse({
    description: 'Lấy danh sách memories thành công',
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách memories thành công',
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              agentId: '456e7890-e89b-12d3-a456-************',
              structuredContent: {
                title: 'JavaScript Programming',
                content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
                reason: 'Để hỗ trợ lập trình web hiệu quả'
              },
              metadata: {
                source: 'training',
                tags: ['programming', 'javascript', 'web'],
                lastUpdated: 1703123456789
              },
              createdAt: 1703120000000
            },
            {
              id: '789e0123-e89b-12d3-a456-************',
              agentId: '456e7890-e89b-12d3-a456-************',
              structuredContent: {
                title: 'React Framework',
                content: 'Kiến thức về React hooks, state management, và component lifecycle',
                reason: 'Để xây dựng UI hiệu quả'
              },
              metadata: {
                source: 'training',
                tags: ['react', 'frontend', 'javascript'],
                lastUpdated: 1703123456789
              },
              createdAt: 1703120000000
            }
          ],
          meta: {
            totalItems: 45,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 5,
            currentPage: 1,
            hasItems: true
          }
        }
      }
    }
  })
  @ApiForbiddenResponse({
    description: 'Không có quyền truy cập agent này',
    schema: {
      example: {
        success: false,
        message: 'Bạn không có quyền truy cập agent này',
        errorCode: 7101,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Chưa xác thực hoặc token không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Unauthorized',
        statusCode: 401,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  async getAgentMemoriesList(
    @Query() query: QueryAgentMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.agentMemoriesService.getAgentMemoriesList(
      userId,
      query,
    );

    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách memories thành công',
    );
  }

  /**
   * Xóa agent memory
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent memory',
    description: 'Xóa vĩnh viễn một agent memory theo ID. Chỉ có thể xóa memory của agent thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Xóa memory thành công',
    type: ApiResponseDto<boolean>,
    schema: {
      example: {
        success: true,
        message: 'Xóa memory thành công',
        data: true
      }
    }
  })
  @ApiNotFoundResponse({
    description: 'Memory không tồn tại',
    schema: {
      example: {
        success: false,
        message: 'Agent memory không tồn tại',
        errorCode: 7100,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiForbiddenResponse({
    description: 'Không có quyền truy cập agent này',
    schema: {
      example: {
        success: false,
        message: 'Bạn không có quyền truy cập agent này',
        errorCode: 7101,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Chưa xác thực hoặc token không hợp lệ',
    schema: {
      example: {
        success: false,
        message: 'Unauthorized',
        statusCode: 401,
        timestamp: '2024-01-01T00:00:00.000Z'
      }
    }
  })
  async deleteAgentMemory(
    @Param('id') memoryId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.agentMemoriesService.deleteAgentMemory(
      memoryId,
      userId,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
