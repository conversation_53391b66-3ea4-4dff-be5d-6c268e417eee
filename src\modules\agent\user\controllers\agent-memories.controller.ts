import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiBody,
  ApiOkResponse,
} from '@nestjs/swagger';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { AUTH_ERROR_CODE } from '@modules/auth/errors/auth-error.code';
import {
  AgentMemoryListResponseDto,
  AgentMemoryResponseDto,
  QueryAgentMemoryDto,
  UpdateAgentMemoryDto,
} from '../dto/agent-memories';
import { AgentMemoriesService } from '../services';
import { MEMORIES_ERROR_CODES } from '@modules/agent/exceptions';

/**
 * Controller xử lý các API liên quan đến agent memories
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT_MEMORIES)
@Controller('v1/user/agent-memories')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentMemoriesController {
  constructor(private readonly agentMemoriesService: AgentMemoriesService) { }

  /**
   * Cập nhật agent memory
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật agent memory',
    description: 'Cập nhật thông tin của một agent memory theo ID. Chỉ có thể cập nhật memory của agent thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    description: 'Dữ liệu cập nhật agent memory',
    type: UpdateAgentMemoryDto,
    examples: {
      example1: {
        summary: 'Cập nhật memory kỹ năng',
        description: 'Ví dụ cập nhật memory về kỹ năng lập trình',
        value: {
          structuredContent: {
            title: 'JavaScript Programming Advanced',
            content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
            reason: 'Để hỗ trợ lập trình web hiệu quả hơn'
          },
          metadata: {
            source: 'training',
            tags: ['programming', 'javascript', 'web'],
            lastUpdated: 1703123456789
          }
        }
      }
    }
  })
  @ApiOkResponse({
    description: 'Cập nhật memory thành công',
    type: ApiResponseDto<AgentMemoryResponseDto>,
    schema: {
      example: {
        success: true,
        message: 'Cập nhật memory thành công',
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          agentId: '456e7890-e89b-12d3-a456-************',
          structuredContent: {
            title: 'JavaScript Programming Advanced',
            content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
            reason: 'Để hỗ trợ lập trình web hiệu quả hơn'
          },
          metadata: {
            source: 'training',
            tags: ['programming', 'javascript', 'web'],
            lastUpdated: 1703123456789
          },
          createdAt: 1703120000000
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_INVALID_DATA,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async updateAgentMemory(
    @Param('id') memoryId: string,
    @Body() updateData: UpdateAgentMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<AgentMemoryResponseDto>> {
    const result = await this.agentMemoriesService.updateAgentMemory(
      memoryId,
      userId,
      updateData,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách agent memories
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent memories',
    description: 'Lấy danh sách memories của tất cả agents thuộc về user với phân trang và các bộ lọc nâng cao. Hỗ trợ tìm kiếm theo nội dung, lọc theo agent cụ thể, loại memory và tags.',
  })
  @ApiOkResponse({
    description: 'Lấy danh sách memories thành công',
    schema: {
      example: {
        success: true,
        message: 'Lấy danh sách memories thành công',
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              agentId: '456e7890-e89b-12d3-a456-************',
              structuredContent: {
                title: 'JavaScript Programming',
                content: 'Kiến thức về JavaScript ES6+, async/await, và modern frameworks',
                reason: 'Để hỗ trợ lập trình web hiệu quả'
              },
              createdAt: 1703120000000
            },
            {
              id: '789e0123-e89b-12d3-a456-************',
              agentId: '456e7890-e89b-12d3-a456-************',
              structuredContent: {
                title: 'React Framework',
                content: 'Kiến thức về React hooks, state management, và component lifecycle',
                reason: 'Để xây dựng UI hiệu quả'
              },
              createdAt: 1703120000000
            }
          ],
          meta: {
            totalItems: 45,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 5,
            currentPage: 1,
            hasItems: true
          }
        }
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async getAgentMemoriesList(
    @Query() query: QueryAgentMemoryDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<PaginatedResult<AgentMemoryListResponseDto>> {
    const result = await this.agentMemoriesService.getAgentMemoriesList(
      userId,
      query,
    );

    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách memories thành công',
    );
  }

  /**
   * Xóa agent memory
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent memory',
    description: 'Xóa vĩnh viễn một agent memory theo ID. Chỉ có thể xóa memory của agent thuộc về user hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponse({
    description: 'Xóa memory thành công',
    type: ApiResponseDto<boolean>,
    schema: {
      example: {
        success: true,
        message: 'Xóa memory thành công',
        data: true
      }
    }
  })
  @ApiErrorResponse(
    MEMORIES_ERROR_CODES.AGENT_MEMORY_NOT_FOUND,
    MEMORIES_ERROR_CODES.AGENT_MEMORY_ACCESS_DENIED,
    AUTH_ERROR_CODE.INVALID_TOKEN
  )
  async deleteAgentMemory(
    @Param('id') memoryId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.agentMemoriesService.deleteAgentMemory(
      memoryId,
      userId,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
