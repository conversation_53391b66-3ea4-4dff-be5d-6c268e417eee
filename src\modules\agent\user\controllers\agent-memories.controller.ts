import {
  Controller,
  Get,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@shared/guards';
import { CurrentUser } from '@shared/decorators';
import { ApiResponseDto } from '@common/response';
import { AgentMemoriesService } from '../services';
import {
  UpdateAgentMemoryDto,
  QueryAgentMemoryDto,
  AgentMemoryResponseDto,
  AgentMemoryListResponseDto,
} from '../dto/agent-memories';
import { User } from '@modules/user/entities';

/**
 * Controller xử lý các API liên quan đến agent memories
 */
@ApiTags('Agent Memories')
@Controller('v1/user/agent-memories')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentMemoriesController {
  constructor(private readonly agentMemoriesService: AgentMemoriesService) {}

  /**
   * Cập nhật agent memory
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật agent memory',
    description: 'Cập nhật thông tin của một agent memory theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần cập nhật',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật memory thành công',
    type: ApiResponseDto<AgentMemoryResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Memory không tồn tại',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền truy cập agent này',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Chưa xác thực',
  })
  async updateAgentMemory(
    @Param('id') memoryId: string,
    @Body() updateData: UpdateAgentMemoryDto,
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<AgentMemoryResponseDto>> {
    const result = await this.agentMemoriesService.updateAgentMemory(
      memoryId,
      user.id,
      updateData,
    );

    return ApiResponseDto.success(result, 'Cập nhật memory thành công');
  }

  /**
   * Lấy danh sách agent memories
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent memories',
    description: 'Lấy danh sách memories của agents thuộc về user với phân trang và filter',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (mặc định: 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng items per page (mặc định: 10)',
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Từ khóa tìm kiếm trong nội dung memory',
    example: 'javascript',
  })
  @ApiQuery({
    name: 'agentId',
    required: false,
    description: 'Lọc theo agent ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Lọc theo loại memory',
    example: 'skill',
  })
  @ApiQuery({
    name: 'minImportance',
    required: false,
    description: 'Lọc theo mức độ quan trọng tối thiểu',
    example: 5,
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description: 'Lọc theo tags (array)',
    example: ['javascript', 'programming'],
    type: [String],
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách memories thành công',
    type: ApiResponseDto<AgentMemoryListResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền truy cập agent này',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Chưa xác thực',
  })
  async getAgentMemoriesList(
    @Query() query: QueryAgentMemoryDto,
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.agentMemoriesService.getAgentMemoriesList(
      user.id,
      query,
    );

    return ApiResponseDto.paginated(
      result.items,
      result.total,
      result.page,
      result.limit,
      'Lấy danh sách memories thành công',
    );
  }

  /**
   * Xóa agent memory
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent memory',
    description: 'Xóa một agent memory theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của memory cần xóa',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa memory thành công',
    type: ApiResponseDto<boolean>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Memory không tồn tại',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Không có quyền truy cập agent này',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Chưa xác thực',
  })
  async deleteAgentMemory(
    @Param('id') memoryId: string,
    @CurrentUser() user: User,
  ): Promise<ApiResponseDto<boolean>> {
    const result = await this.agentMemoriesService.deleteAgentMemory(
      memoryId,
      user.id,
    );

    return ApiResponseDto.success(result, 'Xóa memory thành công');
  }
}
