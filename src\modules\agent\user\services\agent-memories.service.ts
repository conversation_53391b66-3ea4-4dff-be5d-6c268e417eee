import { Injectable, Logger } from '@nestjs/common';
import { AgentMemoriesRepository, AgentUserRepository } from '@modules/agent/repositories';
import { 
  UpdateAgentMemoryDto, 
  QueryAgentMemoryDto, 
  AgentMemoryResponseDto,
  AgentMemoryListResponseDto 
} from '../dto/agent-memories';
import { PaginatedResult } from '@common/response';
import { AppException } from '@common/exceptions';
import { AgentMemories } from '@modules/agent/entities';

/**
 * Service xử lý logic nghiệp vụ cho agent memories
 */
@Injectable()
export class AgentMemoriesService {
  private readonly logger = new Logger(AgentMemoriesService.name);

  constructor(
    private readonly agentMemoriesRepository: AgentMemoriesRepository,
    private readonly agentUserRepository: AgentUserRepository,
  ) {}

  /**
   * Cập nhật agent memory
   * @param memoryId ID của memory cần cập nhật
   * @param userId ID của người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns AgentMemoryResponseDto
   */
  async updateAgentMemory(
    memoryId: string,
    userId: number,
    updateData: UpdateAgentMemoryDto,
  ): Promise<AgentMemoryResponseDto> {
    try {
      this.logger.log(`Updating agent memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId },
      });

      if (!existingMemory) {
        throw new AppException('MEMORY_NOT_FOUND', 'Memory không tồn tại');
      }

      // Kiểm tra user có quyền truy cập agent này không
      const agentUser = await this.agentUserRepository.findOne({
        where: { agentId: existingMemory.agentId, userId },
      });

      if (!agentUser) {
        throw new AppException('ACCESS_DENIED', 'Bạn không có quyền truy cập agent này');
      }

      // Cập nhật memory
      const updateResult = await this.agentMemoriesRepository.update(
        { id: memoryId },
        {
          ...(updateData.structuredContent && { structuredContent: updateData.structuredContent }),
          ...(updateData.metadata && { metadata: updateData.metadata }),
        },
      );

      if (updateResult.affected === 0) {
        throw new AppException('UPDATE_MEMORY_FAILED', 'Không thể cập nhật memory');
      }

      // Lấy memory đã cập nhật
      const updatedMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId },
      });

      if (!updatedMemory) {
        throw new AppException('MEMORY_NOT_FOUND', 'Không thể lấy memory sau khi cập nhật');
      }

      this.logger.log(`Successfully updated agent memory ${memoryId} for user ${userId}`);

      return this.mapToResponseDto(updatedMemory);
    } catch (error) {
      this.logger.error(`Error updating agent memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException('UPDATE_MEMORY_ERROR', 'Lỗi khi cập nhật memory');
    }
  }

  /**
   * Lấy danh sách agent memories với phân trang
   * @param userId ID của người dùng
   * @param query Tham số query
   * @returns AgentMemoryListResponseDto
   */
  async getAgentMemoriesList(
    userId: number,
    query: QueryAgentMemoryDto,
  ): Promise<AgentMemoryListResponseDto> {
    try {
      this.logger.log(`Getting agent memories list for user ${userId}`);

      let memories: AgentMemories[] = [];
      let total = 0;

      // Nếu có filter theo agentId
      if (query.agentId) {
        // Kiểm tra user có quyền truy cập agent này không
        const agentUser = await this.agentUserRepository.findOne({
          where: { agentId: query.agentId, userId },
        });

        if (!agentUser) {
          throw new AppException('ACCESS_DENIED', 'Bạn không có quyền truy cập agent này');
        }

        // Lấy memories theo agentId
        memories = await this.agentMemoriesRepository.findByAgentId(query.agentId);
      } 
      // Nếu có filter theo type
      else if (query.type && query.agentId) {
        memories = await this.agentMemoriesRepository.findByType(query.agentId, query.type);
      }
      // Nếu có filter theo importance
      else if (query.minImportance && query.agentId) {
        memories = await this.agentMemoriesRepository.findByImportance(query.agentId, query.minImportance);
      }
      // Nếu có filter theo tags
      else if (query.tags && query.tags.length > 0 && query.agentId) {
        memories = await this.agentMemoriesRepository.findByTags(query.agentId, query.tags);
      }
      // Nếu có search
      else if (query.search && query.agentId) {
        memories = await this.agentMemoriesRepository.searchByContent(query.agentId, query.search);
      }
      // Nếu không có agentId thì lấy tất cả memories của user
      else {
        // Lấy tất cả agents của user
        const userAgents = await this.agentUserRepository.find({
          where: { userId },
          select: ['agentId'],
        });

        if (userAgents.length === 0) {
          return {
            items: [],
            total: 0,
            page: query.page,
            limit: query.limit,
            totalPages: 0,
            hasItems: false,
          };
        }

        const agentIds = userAgents.map(ua => ua.agentId);
        
        // Lấy memories của tất cả agents
        for (const agentId of agentIds) {
          const agentMemories = await this.agentMemoriesRepository.findByAgentId(agentId);
          memories.push(...agentMemories);
        }
      }

      total = memories.length;

      // Áp dụng phân trang
      const startIndex = (query.page - 1) * query.limit;
      const endIndex = startIndex + query.limit;
      const paginatedMemories = memories.slice(startIndex, endIndex);

      const responseItems = paginatedMemories.map(memory => this.mapToResponseDto(memory));

      this.logger.log(`Successfully retrieved ${responseItems.length} agent memories for user ${userId}`);

      return {
        items: responseItems,
        total,
        page: query.page,
        limit: query.limit,
        totalPages: Math.ceil(total / query.limit),
        hasItems: responseItems.length > 0,
      };
    } catch (error) {
      this.logger.error(`Error getting agent memories list for user ${userId}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException('GET_MEMORIES_ERROR', 'Lỗi khi lấy danh sách memories');
    }
  }

  /**
   * Xóa agent memory
   * @param memoryId ID của memory cần xóa
   * @param userId ID của người dùng
   * @returns boolean
   */
  async deleteAgentMemory(memoryId: string, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Deleting agent memory ${memoryId} for user ${userId}`);

      // Kiểm tra memory có tồn tại không
      const existingMemory = await this.agentMemoriesRepository.findOne({
        where: { id: memoryId },
      });

      if (!existingMemory) {
        throw new AppException('MEMORY_NOT_FOUND', 'Memory không tồn tại');
      }

      // Kiểm tra user có quyền truy cập agent này không
      const agentUser = await this.agentUserRepository.findOne({
        where: { agentId: existingMemory.agentId, userId },
      });

      if (!agentUser) {
        throw new AppException('ACCESS_DENIED', 'Bạn không có quyền truy cập agent này');
      }

      // Xóa memory
      const deleteResult = await this.agentMemoriesRepository.delete({ id: memoryId });

      if (deleteResult.affected === 0) {
        throw new AppException('DELETE_MEMORY_FAILED', 'Không thể xóa memory');
      }

      this.logger.log(`Successfully deleted agent memory ${memoryId} for user ${userId}`);

      return true;
    } catch (error) {
      this.logger.error(`Error deleting agent memory ${memoryId} for user ${userId}: ${error.message}`, error.stack);
      
      if (error instanceof AppException) {
        throw error;
      }
      
      throw new AppException('DELETE_MEMORY_ERROR', 'Lỗi khi xóa memory');
    }
  }

  /**
   * Map AgentMemories entity sang AgentMemoryResponseDto
   * @param memory AgentMemories entity
   * @returns AgentMemoryResponseDto
   */
  private mapToResponseDto(memory: AgentMemories): AgentMemoryResponseDto {
    return {
      id: memory.id,
      agentId: memory.agentId,
      structuredContent: memory.structuredContent,
      metadata: memory.metadata,
      createdAt: memory.createdAt || Date.now(),
    };
  }
}
